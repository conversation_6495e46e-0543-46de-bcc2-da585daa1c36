<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Publication Filter Test</title>
    <style>
        .publications-section h2 #show-selected-link,
        .publications-section h2 #show-all-link {
            transition: color 0.3s ease, text-decoration 0.3s ease;
            cursor: pointer;
            color: #666;
        }

        .publications-section h2 #show-selected-link:hover,
        .publications-section h2 #show-all-link:hover {
            text-decoration: underline;
        }

        .publications-section h2 #show-selected-link.active,
        .publications-section h2 #show-all-link.active {
            color: #4b2e83;
            font-weight: 600;
        }

        .publication-item {
            display: flex;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <section class="publications-section" id="publications">
        <h2>📝 Publications (<span id="show-all-link">show all by date</span> / <span id="show-selected-link">show selected</span>)</h2>
        <div class="publications-list">
            <div class="publication-item">
                <div>Publication 1 (Selected)</div>
            </div>
            <div class="publication-item">
                <div>Publication 2</div>
            </div>
            <div class="publication-item">
                <div>Publication 3</div>
            </div>
        </div>
    </section>

    <script>
        // 文章筛选功能
        function setupPublicationFilter() {
            const showSelectedLink = document.getElementById('show-selected-link');
            const showAllLink = document.getElementById('show-all-link');
            const publicationItems = document.querySelectorAll('.publications-section .publication-item');

            // 检查元素是否存在
            if (!showSelectedLink || !showAllLink) {
                console.log('Publication filter links not found');
                return;
            }

            console.log('Setting up publication filter with', publicationItems.length, 'items');

            let isShowingSelected = false; // 默认显示所有文章

            // 定义精选文章的索引（从0开始）
            const selectedIndices = [0]; // 第一篇文章为精选

            // 更新UI状态
            function updateUI() {
                if (isShowingSelected) {
                    // 显示精选模式：show selected 是当前状态，show all by date 是链接
                    showSelectedLink.classList.add('active');
                    showSelectedLink.style.cursor = 'default';
                    
                    showAllLink.classList.remove('active');
                    showAllLink.style.cursor = 'pointer';
                } else {
                    // 显示全部模式：show all by date 是当前状态，show selected 是链接
                    showAllLink.classList.add('active');
                    showAllLink.style.cursor = 'default';
                    
                    showSelectedLink.classList.remove('active');
                    showSelectedLink.style.cursor = 'pointer';
                }
            }

            // 显示精选文章
            function showSelectedPublications() {
                publicationItems.forEach((item, index) => {
                    if (selectedIndices.includes(index)) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                });
                isShowingSelected = true;
                updateUI();
                console.log('Showing selected publications');
            }

            // 显示所有文章
            function showAllPublications() {
                publicationItems.forEach(item => {
                    item.style.display = 'flex';
                });
                isShowingSelected = false;
                updateUI();
                console.log('Showing all publications');
            }

            // 点击事件
            showSelectedLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Show selected clicked, current state:', isShowingSelected);
                if (!isShowingSelected) {
                    showSelectedPublications();
                }
            });

            showAllLink.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Show all clicked, current state:', isShowingSelected);
                if (isShowingSelected) {
                    showAllPublications();
                }
            });

            // 初始化
            showAllPublications();
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function () {
            setupPublicationFilter();
        });
    </script>
</body>
</html>
